import React from "react";
import { motion } from "framer-motion";
import { Rocket } from "lucide-react";
import { <PERSON> } from "wouter";
import { useCases } from "./data";

const CTASection: React.FC = () => {
  return (
    <section className="py-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-purple-100 via-blue-50 to-white">
      <div className="container mx-auto">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl sm:text-5xl font-black mb-6">
                Transforma Tu Negocio <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"><PERSON><PERSON></span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Únete a cientos de empresas que ya están revolucionando su marketing con nuestros agentes IA.
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {useCases.map((useCase, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-8 text-center"
                whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
              >
                <div className="flex justify-center mb-6">
                  <div className={`${useCase.color} p-4 rounded-full border-3 border-black`}>
                    {useCase.icon}
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-4">{useCase.title}</h3>
                <p className="text-gray-600">
                  {useCase.description}
                </p>
              </motion.div>
            ))}
          </div>

          <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-8 text-white text-center">
            <h3 className="text-3xl font-black mb-6">¿Listo para revolucionar tu marketing?</h3>
            <p className="text-xl mb-8">
              Empieza hoy mismo con un agente IA especializado y lleva tu negocio al siguiente nivel.
            </p>
            <div className="flex justify-center">
              <Link href="/login">
                <motion.button
                  className="bg-white text-purple-600 font-black py-4 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-gray-50 transition-all duration-300 w-full sm:w-auto"
                  whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
                  whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
                >
                  <span className="flex items-center justify-center gap-2">
                    Empezar Ahora <Rocket size={20} />
                  </span>
                </motion.button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
