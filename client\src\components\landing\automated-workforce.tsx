import { motion } from "framer-motion";
import { Link } from "wouter";
import { Check } from "lucide-react";

export default function AutomatedWorkforce() {
  const benefits = [
    "Los agentes de IA trabajan 24/7 sin descansos ni turnos",
    "Generan y optimizan contenido sin intervención humana",
    "Gestionan campañas completas de forma autónoma",
    "Aprenden y mejoran constantemente",
    "Escalan según tus necesidades sin costos adicionales",
    "Trabajan en múltiples canales simultáneamente",
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-xl"
          >
            <motion.h2
              className="text-3xl sm:text-4xl font-black mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <motion.span
                className="bg-white inline-block px-4 py-2 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-3"
                whileHover={{
                  y: -5,
                  boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
                }}
              >
                Equipo de Marketing Automatizado
              </motion.span>
              <br />
              <span className="text-2xl mt-2 block">
                Tu Equipo de IA, Listo para Trabajar
              </span>
            </motion.h2>

            <motion.p
              className="text-lg mb-8 font-medium"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              En lugar de contratar empleados costosos, construye un equipo de
              agentes de IA especializados que nunca duermen, nunca se toman
              vacaciones y están optimizados para ofrecer resultados
              excepcionales.
            </motion.p>

            <motion.div
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  className="flex items-center bg-white p-3 rounded-lg border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)]"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                  whileHover={{
                    x: 5,
                    boxShadow: "5px 5px 0px 0px rgba(0,0,0,0.9)",
                  }}
                >
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3 border-2 border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.9)]">
                    <Check size={16} className="text-white" />
                  </div>
                  <p className="text-sm font-bold">{benefit}</p>
                </motion.div>
              ))}
            </motion.div>

            <Link href="/auth">
              <motion.button
                className="mt-10 bg-blue-500 text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-blue-600 transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1 }}
                whileHover={{
                  y: -5,
                  boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
                }}
                whileTap={{
                  y: 0,
                  boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)",
                }}
              >
                Contrata Empleados IA Ahora
              </motion.button>
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="relative"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-200 via-purple-200 to-pink-200 rounded-3xl transform rotate-3 scale-105 opacity-70"></div>
            <div className="bg-white p-8 rounded-2xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] relative">
              <div className="grid grid-cols-2 gap-6">
                {[
                  {
                    title: "AI Copywriter",
                    desc: "Crea contenido persuasivo",
                    icon: "📝",
                    color: "blue",
                  },
                  {
                    title: "AI Designer",
                    desc: "Genera gráficos y diseños",
                    icon: "🎨",
                    color: "pink",
                  },
                  {
                    title: "AI Ad Manager",
                    desc: "Optimiza campañas publicitarias",
                    icon: "📊",
                    color: "purple",
                  },
                  {
                    title: "AI Social Media",
                    desc: "Gestiona redes sociales",
                    icon: "📱",
                    color: "green",
                  },
                ].map((agent, index) => (
                  <motion.div
                    key={index}
                    className="bg-white rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] p-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                    whileHover={{
                      y: -5,
                      boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
                    }}
                  >
                    <div className="text-2xl mb-2">{agent.icon}</div>
                    <h3
                      className={`font-black text-sm text-${agent.color}-500`}
                    >
                      {agent.title}
                    </h3>
                    <p className="text-xs mt-1">{agent.desc}</p>
                  </motion.div>
                ))}
              </div>

              <motion.div
                className="mt-6 bg-gray-100 rounded-xl border-2 border-black p-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
              >
                <div className="flex items-center mb-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-1 border border-black"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-1 border border-black"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-1 border border-black"></div>
                  <div className="text-xs font-bold ml-2">
                    AI-Agent Dashboard
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="w-1/2 h-6 bg-gray-200 rounded border border-black"></div>
                  <div className="flex space-x-1">
                    <div className="w-6 h-6 bg-blue-200 rounded-full border border-black"></div>
                    <div className="w-6 h-6 bg-green-200 rounded-full border border-black"></div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
