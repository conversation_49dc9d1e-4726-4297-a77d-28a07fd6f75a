import { motion } from "framer-motion";

export default function HowItWorks() {
  const steps = [
    {
      number: "1",
      title: "Define tu Meta",
      description:
        "Establece tus objetivos de marketing específicos, audiencia y resultados deseados.",
      color: "blue",
    },
    {
      number: "2",
      title: "Ensambla tu Equipo",
      description:
        "Selecciona los especialistas IA ideales para tus necesidades específicas de marketing.",
      color: "pink",
    },
    {
      number: "3",
      title: "La IA Ejecuta",
      description:
        "Tu equipo IA colabora, crea y optimiza tus campañas automáticamente.",
      color: "green",
    },
    {
      number: "4",
      title: "Mide y Escala",
      description:
        "Analiza resultados en tiempo real y escala tus éxitos instantáneamente.",
      color: "purple",
    },
  ];

  return (
    <section id="comofunciona" className="py-20 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Cómo Funciona
          </h2>
          <p className="text-xl font-bold max-w-3xl mx-auto">
            Un proceso sencillo para revolucionar tu marketing
          </p>
        </motion.div>

        <div className="max-w-5xl mx-auto">
          <div className="relative">
            {/* Connection Line */}
            <div className="absolute top-1/2 left-0 right-0 h-2 bg-black transform -translate-y-1/2 hidden md:block"></div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {steps.map((step, index) => (
                <motion.div
                  key={index}
                  className="relative"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-50px" }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                >
                  <motion.div
                    className="bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 h-full hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] transition-all duration-200 transform hover:-translate-y-2"
                    whileHover={{ y: -5 }}
                  >
                    <motion.div
                      className={`w-16 h-16 mx-auto bg-${step.color}-500 rounded-full border-3 border-black flex items-center justify-center text-white font-black text-2xl mb-4`}
                      animate={{
                        y: [0, -5, 0],
                        scale: [1, 1.05, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: index * 0.2,
                      }}
                    >
                      {step.number}
                    </motion.div>
                    <h3 className="text-xl font-black text-center mb-3">
                      {step.title}
                    </h3>
                    <p className="text-center">{step.description}</p>
                  </motion.div>
                </motion.div>
              ))}
            </div>
          </div>

          <motion.div
            className="mt-16 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <motion.button
              className="bg-black text-white text-xl font-black py-4 px-10 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-white hover:text-black transition-colors duration-200"
              whileHover={{
                y: -5,
                boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
              }}
              whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
            >
              Ver Demostración →
            </motion.button>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
