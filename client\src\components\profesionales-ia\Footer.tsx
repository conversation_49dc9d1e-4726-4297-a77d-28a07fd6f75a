import React from "react";
import { Zap, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin, Github } from "lucide-react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white py-16 px-4 sm:px-6 lg:px-8 border-t-4 border-purple-600">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          {/* Logo y descripción */}
          <div className="col-span-1 md:col-span-1">
            <div className="flex items-center mb-6">
              <div className="bg-purple-600 w-10 h-10 rounded-full flex items-center justify-center text-white border-3 border-white mr-2">
                <Zap size={20} />
              </div>
              <span className="font-black text-xl">EmmaAI</span>
            </div>
            <p className="text-gray-400 mb-6">
              Profesionales IA especializados que transforman tu negocio con resultados reales, disponibles 24/7.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Linkedin size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Github size={20} />
              </a>
            </div>
          </div>

          {/* Enlaces rápidos */}
          <div>
            <h3 className="font-bold text-lg mb-6 text-purple-400">Enlaces Rápidos</h3>
            <ul className="space-y-3">
              <li>
                <a href="#que-es" className="text-gray-400 hover:text-white transition-colors">¿Qué es?</a>
              </li>
              <li>
                <a href="#como-funciona" className="text-gray-400 hover:text-white transition-colors">Cómo Funciona</a>
              </li>
              <li>
                <a href="#beneficios" className="text-gray-400 hover:text-white transition-colors">Beneficios</a>
              </li>
              <li>
                <a href="#agentes" className="text-gray-400 hover:text-white transition-colors">Agentes</a>
              </li>
              <li>
                <a href="#testimonios" className="text-gray-400 hover:text-white transition-colors">Testimonios</a>
              </li>
              <li>
                <a href="#preguntas" className="text-gray-400 hover:text-white transition-colors">FAQ</a>
              </li>
            </ul>
          </div>

          {/* Servicios */}
          <div>
            <h3 className="font-bold text-lg mb-6 text-purple-400">Servicios</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">Copywriting IA</a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">Diseño UX/UI</a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">Estrategia de Marketing</a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">Análisis de Datos</a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">Email Marketing</a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">Redes Sociales</a>
              </li>
            </ul>
          </div>

          {/* Contacto */}
          <div>
            <h3 className="font-bold text-lg mb-6 text-purple-400">Contacto</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <Mail className="mr-3 text-purple-400 mt-1" size={18} />
                <span className="text-gray-400"><EMAIL></span>
              </li>
              <li className="flex items-start">
                <Phone className="mr-3 text-purple-400 mt-1" size={18} />
                <span className="text-gray-400">+52 55 1234 5678</span>
              </li>
              <li className="flex items-start">
                <MapPin className="mr-3 text-purple-400 mt-1" size={18} />
                <span className="text-gray-400">
                  Av. Revolución 123, Col. Escandón
                  <br />
                  Ciudad de México, CP 11800
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-500 text-sm mb-4 md:mb-0">
            &copy; {new Date().getFullYear()} EmmaAI. Todos los derechos reservados.
          </p>
          <div className="flex space-x-6">
            <a href="#" className="text-gray-500 hover:text-white text-sm transition-colors">
              Términos y Condiciones
            </a>
            <a href="#" className="text-gray-500 hover:text-white text-sm transition-colors">
              Política de Privacidad
            </a>
            <a href="#" className="text-gray-500 hover:text-white text-sm transition-colors">
              Cookies
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
