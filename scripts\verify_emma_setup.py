#!/usr/bin/env python3
"""
🤖 Emma AI - Script de Verificación de Configuración

Este script verifica que Emma AI esté correctamente configurada e integrada.
Útil para desarrolladores que se unen al proyecto o para debugging.

Uso: python scripts/verify_emma_setup.py
"""

import os
import sys
import requests
import json
from pathlib import Path

# Colores para output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_status(message, status="info"):
    """Imprime mensaje con color según el status"""
    if status == "success":
        print(f"{Colors.GREEN}✅ {message}{Colors.END}")
    elif status == "error":
        print(f"{Colors.RED}❌ {message}{Colors.END}")
    elif status == "warning":
        print(f"{Colors.YELLOW}⚠️  {message}{Colors.END}")
    else:
        print(f"{Colors.BLUE}ℹ️  {message}{Colors.END}")

def check_file_exists(file_path, description):
    """Verifica si un archivo existe"""
    if os.path.exists(file_path):
        print_status(f"{description}: {file_path}", "success")
        return True
    else:
        print_status(f"{description} NO ENCONTRADO: {file_path}", "error")
        return False

def check_env_var(var_name, description):
    """Verifica si una variable de entorno está configurada"""
    value = os.getenv(var_name)
    if value:
        print_status(f"{description}: Configurada", "success")
        return True
    else:
        print_status(f"{description}: NO CONFIGURADA", "error")
        return False

def check_port_available(port, service_name):
    """Verifica si un puerto está disponible o en uso"""
    try:
        response = requests.get(f"http://localhost:{port}", timeout=2)
        print_status(f"{service_name} (puerto {port}): Activo", "success")
        return True
    except:
        print_status(f"{service_name} (puerto {port}): No disponible", "warning")
        return False

def main():
    print(f"{Colors.BOLD}{Colors.BLUE}")
    print("🤖 Emma AI - Verificación de Configuración")
    print("=" * 50)
    print(f"{Colors.END}")
    
    errors = 0
    warnings = 0
    
    # 1. Verificar estructura de archivos
    print(f"\n{Colors.BOLD}📁 Verificando Estructura de Archivos{Colors.END}")
    
    files_to_check = [
        ("client/src/pages/emma-agenticseek-page.tsx", "Interfaz principal Emma AI"),
        ("client/src/pages/EMMA_AI_README.md", "Documentación Emma AI"),
        ("docs/EMMA_AI_ARCHITECTURE.md", "Arquitectura Emma AI"),
        ("backend/app/agenticseek/api.py", "API AgenticSeek"),
        ("backend/app/agenticseek/sources/agents/", "Directorio de agentes"),
        ("emma-integration/emma_orchestrator.py", "Emma Orchestrator"),
        ("backend/app/agenticseek/frontend/agentic-seek-front/src/App.jsx", "Frontend AgenticSeek"),
    ]
    
    for file_path, description in files_to_check:
        if not check_file_exists(file_path, description):
            errors += 1
    
    # 2. Verificar variables de entorno
    print(f"\n{Colors.BOLD}🔑 Verificando Variables de Entorno{Colors.END}")
    
    env_vars = [
        ("GEMINI_API_KEY", "Gemini API Key"),
        ("SERPER_API_KEY", "Serper API Key"),
        ("BROWSERLESS_API_KEY", "Browserless API Key"),
        ("STABILITY_API_KEY", "Stability AI API Key"),
    ]
    
    for var_name, description in env_vars:
        if not check_env_var(var_name, description):
            errors += 1
    
    # 3. Verificar puertos
    print(f"\n{Colors.BOLD}🌐 Verificando Servicios{Colors.END}")
    
    services = [
        (3002, "Frontend Emma Studio (Vite)"),
        (8001, "Backend Emma Studio (FastAPI)"),
        (5173, "Frontend alternativo"),
    ]
    
    for port, service_name in services:
        if not check_port_available(port, service_name):
            warnings += 1
    
    # 4. Verificar configuración de AgenticSeek
    print(f"\n{Colors.BOLD}🔧 Verificando Configuración AgenticSeek{Colors.END}")
    
    agenticseek_files = [
        ("backend/app/agenticseek/config.ini", "Configuración AgenticSeek"),
        ("backend/app/agenticseek/requirements.txt", "Dependencias AgenticSeek"),
        ("backend/app/agenticseek/sources/llm_provider.py", "Proveedor LLM"),
        ("backend/app/agenticseek/sources/browser.py", "Browser Agent"),
    ]
    
    for file_path, description in agenticseek_files:
        if not check_file_exists(file_path, description):
            errors += 1
    
    # 5. Verificar rutas en App.tsx
    print(f"\n{Colors.BOLD}🛣️  Verificando Rutas{Colors.END}")
    
    try:
        with open("client/src/App.tsx", "r") as f:
            app_content = f.read()
            
        routes_to_check = ["/emma-ai", "/emma-agenticseek", "/dashboard/emma-ai"]
        for route in routes_to_check:
            if route in app_content:
                print_status(f"Ruta {route}: Configurada", "success")
            else:
                print_status(f"Ruta {route}: NO ENCONTRADA", "error")
                errors += 1
                
    except Exception as e:
        print_status(f"Error leyendo App.tsx: {e}", "error")
        errors += 1
    
    # 6. Resumen final
    print(f"\n{Colors.BOLD}📊 Resumen de Verificación{Colors.END}")
    print("-" * 30)
    
    if errors == 0 and warnings == 0:
        print_status("¡Emma AI está perfectamente configurada! 🎉", "success")
    elif errors == 0:
        print_status(f"Emma AI está configurada con {warnings} advertencias", "warning")
    else:
        print_status(f"Emma AI tiene {errors} errores y {warnings} advertencias", "error")
    
    # 7. Próximos pasos
    print(f"\n{Colors.BOLD}🚀 Próximos Pasos{Colors.END}")
    
    if errors > 0:
        print("Para corregir errores:")
        print("1. Revisar documentación: client/src/pages/EMMA_AI_README.md")
        print("2. Verificar variables de entorno en backend/.env")
        print("3. Ejecutar: python backend/test_emma_agenticseek.py")
    else:
        print("Emma AI está lista para usar:")
        print("1. Iniciar frontend: cd client && npm run dev")
        print("2. Iniciar backend: cd backend && python -m uvicorn app.main:app --reload --port 8001")
        print("3. Acceder a: http://localhost:3002/emma-ai")
    
    print(f"\n{Colors.BOLD}📚 Documentación Útil{Colors.END}")
    print("- Guía rápida: client/src/pages/EMMA_AI_README.md")
    print("- Arquitectura: docs/EMMA_AI_ARCHITECTURE.md")
    print("- Tests: backend/test_emma_agenticseek.py")
    
    return errors

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
