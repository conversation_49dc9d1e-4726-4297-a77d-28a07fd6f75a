import { useState } from "react";
import { useLocation } from "wouter";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  AlertCircle,
  ArrowRight,
  Brain,
  Check,
  DollarSign,
  FileText,
  Megaphone,
  Minus,
  Star,
  Users,
  Zap,
} from "lucide-react";

// Componente de página de servicios humanos con estilo actualizado
export default function HumanServicesPageNew() {
  const [location, navigate] = useLocation();

  // Datos fijos de los dos proveedores con el contenido actualizado
  const providers = [
    {
      id: "emmaai",
      title: "Emma AI Team",
      subtitle: "Marketing humano en 48 horas",
      headline:
        "La plataforma de Emma AI lo hace. Pídelo y lo tendrás en menos de 48 horas.",
      description:
        "Todo lo que haría una agencia de marketing está optimizado con IA para mejores resultados a menor costo. Obtén todo tipo de servicios profesionales de marketing ejecutados por expertos humanos respaldados por IA.",
      services: [
        "Campañas completas en redes sociales con creatividades profesionales",
        "Email marketing y copywriting de alto nivel por escritores expertos",
        "Diseño gráfico y branding con acabados profesionales",
        "Funnels de conversión completos con optimización continua",
        "Creación de contenido visual: Reels, carruseles y videos con edición profesional",
      ],
      tagline:
        "Mejor que cualquier agencia de marketing tradicional, a una fracción del costo.",
      buttonText: "Pedir servicio ahora",
      icon: Megaphone,
    },
    {
      id: "marwick",
      title: "Marwick Consulting",
      subtitle: "Empleados virtuales. Consultoría real.",
      headline:
        "Construimos empleados virtuales que reemplazan equipos completos.",
      description:
        "Tienes en la mano a la mejor consultora de IA de Latinoamérica, especializada en agentes y automatización de negocios.",
      services: [
        "Crear agentes inteligentes a la medida (ventas, soporte, contenido, etc.)",
        "Automatizar y escalar tu empresa sin contratar más humanos",
        "Reemplazar departamentos enteros con sistemas IA",
        "Consultoría estratégica mensual con expertos en inteligencia artificial",
        "Infraestructura e integraciones técnicas de alto nivel",
      ],
      tagline:
        "Sustituye empleados tradicionales por agentes IA que trabajan 24/7 sin vacaciones ni errores.",
      buttonText: "Solicitar mi agente personalizado",
      icon: Brain,
    },
  ];

  return (
    <DashboardLayout pageTitle="Servicios Profesionales">
      <div className="min-h-screen bg-[#f5f5f5] py-10 px-4 relative">
        {/* Background particles y grid pattern */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="absolute bg-black/5 rounded-full"
              style={{
                width: `${Math.random() * 16 + 4}px`,
                height: `${Math.random() * 16 + 4}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
            />
          ))}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage:
                "linear-gradient(#00000008 1px, transparent 1px), linear-gradient(90deg, #00000008 1px, transparent 1px)",
              backgroundSize: "16px 16px",
            }}
          />
        </div>

        <div className="container mx-auto max-w-6xl relative z-10">
          {/* Encabezado con estilo landing page */}
          <div className="mb-12 text-center">
            <h1 className="inline-block text-3xl sm:text-4xl font-black mb-4 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-transparent bg-clip-text px-6 py-3 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] bg-white">
              Servicios Profesionales
            </h1>
            <p className="text-xl max-w-3xl mx-auto bg-white p-4 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]">
              Conecta con nuestros equipos especializados para proyectos que
              exigen calidad humana
            </p>
          </div>

          {/* Mensaje de valor diferencial */}
          <div className="bg-white p-6 mb-10 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]">
            <div className="flex items-center mb-3">
              <AlertCircle className="h-6 w-6 text-amber-600 mr-2 flex-shrink-0" />
              <h3 className="text-lg font-black">
                ¡Olvídate de las agencias de marketing y las contrataciones
                tradicionales!
              </h3>
            </div>
            <p className="text-gray-700 mb-4">
              En Emma AI, hemos reinventado completamente cómo funcionan los
              servicios profesionales. Combinando la potencia de nuestra
              plataforma de IA con expertos humanos especializados, podemos
              ofrecerte lo mejor de ambos mundos: velocidad, calidad y precios
              increíbles.
            </p>

            {/* Comparativa rápida */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div className="bg-red-50 p-4 rounded-lg border-2 border-black">
                <h4 className="font-bold mb-2 flex items-center">
                  <Minus className="w-4 h-4 text-red-500 mr-2" />
                  Agencias tradicionales
                </h4>
                <p className="text-sm text-gray-700">
                  Contratos largos, precios inflados, entregas lentas y
                  resultados impredecibles.
                </p>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg border-2 border-black">
                <h4 className="font-bold mb-2 flex items-center">
                  <Check className="w-4 h-4 text-green-500 mr-2" />
                  Emma AI Team
                </h4>
                <p className="text-sm text-gray-700">
                  Marketing profesional bajo demanda, entregas en 48h, sin
                  contratos largos.
                </p>
              </div>

              <div className="bg-indigo-50 p-4 rounded-lg border-2 border-black">
                <h4 className="font-bold mb-2 flex items-center">
                  <Check className="w-4 h-4 text-green-500 mr-2" />
                  Marwick Consulting
                </h4>
                <p className="text-sm text-gray-700">
                  Empleados virtuales de IA que reemplazan equipos completos a
                  una fracción del costo.
                </p>
              </div>
            </div>
          </div>

          {/* Sección de proveedores destacados - diseño actualizado */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {providers.map((provider) => (
              <div
                key={provider.id}
                className="bg-white rounded-xl border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden transform transition-all duration-300 hover:-translate-y-1 hover:shadow-[10px_10px_0px_0px_rgba(0,0,0,0.9)]"
              >
                <div className="flex flex-col h-full">
                  {/* Cabecera de imagen */}
                  <div className="flex items-center justify-center py-10 px-8 bg-white border-b-3 border-black">
                    <img
                      src={
                        provider.id === "emmaai"
                          ? "/assets/DAE78D45-1C24-45E7-9A80-507D0FFB90B7.png"
                          : "/assets/A2DA7EE5-A2B8-4D64-8331-8FAFC4355ED5.png"
                      }
                      alt={provider.title}
                      className="h-36 object-contain"
                    />
                  </div>

                  <div
                    className={`p-6 flex-1 ${provider.id === "emmaai" ? "bg-gradient-to-br from-blue-50 to-indigo-50" : "bg-gradient-to-br from-gray-50 to-slate-100"}`}
                  >
                    {/* Badge de subtítulo */}
                    <div className="mb-4">
                      <span className="inline-flex items-center text-sm font-black rounded-full px-4 py-1 border-2 border-black bg-white shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">
                        {provider.id === "emmaai" ? (
                          <Zap className="h-4 w-4 mr-1 text-blue-600" />
                        ) : (
                          <Brain className="h-4 w-4 mr-1 text-gray-700" />
                        )}
                        {provider.subtitle}
                      </span>
                    </div>

                    <h2 className="text-2xl font-black mb-3">
                      {provider.title}
                    </h2>

                    <p className="font-medium text-lg mb-3">
                      {provider.headline}
                    </p>

                    <div className="mb-4 bg-white p-3 rounded-lg border-2 border-black">
                      <div className="flex items-center mb-2">
                        <Star className="h-4 w-4 text-amber-500 mr-2 fill-amber-500" />
                        <span className="font-bold text-sm">
                          ¿Por qué elegir{" "}
                          {provider.id === "emmaai"
                            ? "Emma AI Team"
                            : "Marwick"}
                          ?
                        </span>
                      </div>
                      <p className="text-sm text-gray-700">
                        {provider.description}
                      </p>
                    </div>

                    <div className="mb-6">
                      <h3 className="font-black mb-3 flex items-center">
                        <Check className="h-5 w-5 text-green-600 mr-2" />
                        {provider.id === "emmaai"
                          ? "Servicios disponibles:"
                          : "¿Qué puedes hacer con Marwick?"}
                      </h3>
                      <ul className="space-y-2 bg-white p-3 rounded-lg border-2 border-black">
                        {provider.services.map((service, index) => (
                          <li key={index} className="flex items-start text-sm">
                            <DollarSign className="h-4 w-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                            <span>{service}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-yellow-50 rounded-lg p-3 border-2 border-black mb-4">
                      <p className="text-sm font-bold">{provider.tagline}</p>
                    </div>

                    <Button
                      className="py-3 px-6 w-full bg-black hover:bg-gray-800 text-white font-black rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] transform transition-all duration-300 hover:-translate-y-1 hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]"
                      onClick={() =>
                        provider.id === "marwick"
                          ? navigate("/marwick")
                          : navigate("/emma-team")
                      }
                    >
                      <span>{provider.buttonText}</span>
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Sección de complementariedad */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] mb-10">
            <h3 className="text-xl font-black mb-4">
              Combina ambos servicios para un resultado increíble
            </h3>
            <p className="text-gray-700 mb-6">
              Nuestros servicios están diseñados para complementarse. Contrata a
              Emma AI Team para tus necesidades de marketing y a Marwick para
              automatizar tu empresa con agentes inteligentes. La combinación
              perfecta para escalar tu negocio sin los dolores de cabeza
              tradicionales.
            </p>
            <div className="flex items-center justify-center gap-4 md:gap-10">
              <div className="text-center">
                <div className="bg-white p-3 inline-flex rounded-full mb-2 border-2 border-black">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <p className="font-bold">Equipo humano</p>
              </div>

              <div className="text-3xl font-black">+</div>

              <div className="text-center">
                <div className="bg-white p-3 inline-flex rounded-full mb-2 border-2 border-black">
                  <FileText className="h-6 w-6 text-gray-700" />
                </div>
                <p className="font-bold">Automatización IA</p>
              </div>

              <div className="text-3xl font-black">=</div>

              <div className="text-center">
                <div className="bg-white p-3 inline-flex rounded-full mb-2 border-2 border-black">
                  <Star className="h-6 w-6 text-amber-500 fill-amber-500" />
                </div>
                <p className="font-bold">Crecimiento exponencial</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
