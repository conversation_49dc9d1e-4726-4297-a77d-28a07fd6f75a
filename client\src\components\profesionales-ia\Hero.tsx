import React, { useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Rocket, Play } from "lucide-react";
import { <PERSON> } from "wouter";
import { rotatingTexts } from "./data";

interface HeroProps {
  textIndex: number;
}

const Hero: React.FC<HeroProps> = ({ textIndex }) => {
  const heroRef = useRef<HTMLDivElement>(null);

  return (
    <section ref={heroRef} className="pt-40 pb-24 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 z-0"></div>
      
      {/* Elementos decorativos */}
      <motion.div 
        className="absolute top-40 right-[10%] w-20 h-20 rounded-full bg-yellow-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
        animate={{ 
          y: [0, -20, 0],
          rotate: [0, 10, 0]
        }}
        transition={{ 
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      />
      
      <motion.div 
        className="absolute bottom-20 left-[10%] w-16 h-16 rounded-full bg-blue-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
        animate={{ 
          y: [0, 15, 0],
          rotate: [0, -8, 0]
        }}
        transition={{ 
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1
        }}
      />

      <motion.div 
        className="absolute top-60 left-[15%] w-12 h-12 rounded-full bg-pink-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
        animate={{ 
          y: [0, 10, 0],
          x: [0, 10, 0]
        }}
        transition={{ 
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 2
        }}
      />
      
      <div className="container mx-auto relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-5xl sm:text-6xl md:text-7xl font-black mb-6 leading-tight">
              Profesionales IA <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">
                Listos para Revolucionar
              </span> <br />
              Tu Negocio
            </h1>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <p className="text-xl sm:text-2xl font-medium mb-4 text-gray-700">
              Contrata agentes IA especializados que trabajan 24/7 para
            </p>
            <div className="text-2xl sm:text-3xl font-bold text-purple-700 mb-8">
              <div className="h-[40px] overflow-hidden">
                <AnimatePresence mode="wait">
                  <motion.span
                    key={rotatingTexts[textIndex]}
                    initial={{ opacity: 0, y: 40 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -40 }}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 20,
                    }}
                    className="block"
                  >
                    {rotatingTexts[textIndex]}
                  </motion.span>
                </AnimatePresence>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link href="/login">
              <motion.button
                className="bg-purple-600 text-white font-black py-4 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-purple-700 transition-all duration-300 w-full sm:w-auto"
                whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
                whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
              >
                <span className="flex items-center justify-center gap-2">
                  Empezar Gratis <Rocket size={20} />
                </span>
              </motion.button>
            </Link>

            <Link href="/login">
              <motion.button
                className="bg-white text-purple-600 font-black py-4 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-gray-50 transition-all duration-300 w-full sm:w-auto"
                whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
                whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
              >
                <span className="flex items-center justify-center gap-2">
                  Ver Demo <Play size={20} />
                </span>
              </motion.button>
            </Link>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
