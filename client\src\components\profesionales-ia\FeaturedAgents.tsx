import React from "react";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import { <PERSON> } from "wouter";
import { featuredAgents } from "./data";

const FeaturedAgents: React.FC = () => {
  return (
    <section id="agentes" className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl sm:text-5xl font-black mb-6">
            Nuestros Agentes Estrella
          </h2>
          <p className="text-xl text-gray-600">
            Profesionales IA especializados, listos para transformar tu negocio
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {featuredAgents.map((agent, index) => (
            <motion.div 
              key={index}
              className="relative"
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 + index * 0.2 }}
              whileHover={{ y: -10 }}
            >
              <div className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden h-full">
                <div className="bg-gradient-to-r from-purple-600 to-purple-700 p-4 text-white">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-bold">Agente Premium</h3>
                    <span className="bg-green-400 text-black px-3 py-1 rounded-full text-xs font-bold border-2 border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.9)]">
                      Disponible
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-6">
                    <div className={`w-20 h-20 ${agent.bgColor} rounded-full flex items-center justify-center border-3 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)]`}>
                      {agent.icon}
                    </div>
                    <div>
                      <h4 className="text-2xl font-black">{agent.name}</h4>
                      <p className="text-purple-600 font-bold">{agent.role}</p>
                    </div>
                  </div>
                  <div className="border-t-3 border-black pt-4">
                    <div className="flex justify-between mb-3">
                      <span className="font-bold">Especialidad:</span>
                      <span className="font-medium">{agent.specialty}</span>
                    </div>
                    <div className="flex justify-between mb-3">
                      <span className="font-bold">Disponibilidad:</span>
                      <span className="font-medium text-green-600">24/7</span>
                    </div>
                    <div className="flex justify-between mb-4">
                      <span className="font-bold">Proyectos completados:</span>
                      <span className="font-medium">{agent.projects}</span>
                    </div>
                    <p className="text-gray-600 mb-6">{agent.description}</p>
                    <Link href="/login">
                      <motion.button
                        className="w-full mt-2 bg-purple-600 text-white font-black py-4 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-purple-700 transition-all duration-300"
                        whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
                        whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
                      >
                        <span className="flex items-center justify-center gap-2">
                          Contratar Ahora <ArrowRight size={18} />
                        </span>
                      </motion.button>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedAgents;
