import React, { useState } from "react";
import {
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  BarChart2,
  Briefcase,
  Check,
  CheckCircle,
  Clock,
  Columns,
  DollarSign,
  Eye,
  Facebook,
  FileText,
  Heart,
  Image,
  Instagram,
  LayoutDashboard,
  LucideIcon,
  Mail,
  Megaphone,
  MessageSquare,
  Minus,
  RefreshCw,
  Search,
  Share2,
  ShoppingCart,
  Sparkles,
  Star,
  TrendingUp,
  Users,
  Video,
} from "lucide-react";
import { Link } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DashboardLayout } from "@/components/layout/dashboard-layout";

// Interfaces para tipado
interface Service {
  id: string;
  title: string;
  description?: string;
  deliveryTime: string;
  icon: LucideIcon;
  bgImage?: string;
  popular?: boolean;
  featured?: boolean;
  stats?: {
    likes?: number;
    views?: number;
  };
}

interface ServiceCategory {
  id: string;
  name: string;
  icon: LucideIcon;
  services: Service[];
}

// Página principal de Emma Team - Estilo Behance minimalista
export default function EmmaTeamV2() {
  // Estado para categoría seleccionada
  const [selectedTab, setSelectedTab] = useState("todos");

  // Manejador para solicitar servicio
  const handleServiceSelect = (service: string) => {
    window.location.href = `/app/create?service=${encodeURIComponent(service)}`;
  };

  // Servicios organizados por categorías
  const serviceCategories: ServiceCategory[] = [
    {
      id: "redes-sociales",
      name: "Redes Sociales",
      icon: Megaphone,
      services: [
        {
          id: "instagram-ads",
          title: "Campaña Instagram Ads",
          description:
            "Campaña completa con 5 creatividades y textos optimizados para maximizar engagement y conversiones",
          deliveryTime: "24-48h",
          icon: Image,
          featured: true,
          stats: {
            likes: 124,
            views: 1890,
          },
        },
        {
          id: "facebook-ads",
          title: "Campaña Facebook Ads",
          description:
            "Campaña completa con 4 formatos diferentes para atraer a tu audiencia ideal",
          deliveryTime: "24-48h",
          icon: TrendingUp,
        },
        {
          id: "reels-tiktok",
          title: "Pack 3 Reels/TikToks",
          description:
            "Guiones completos listos para grabar contenido viral de alto impacto",
          deliveryTime: "48h",
          icon: Columns,
          popular: true,
        },
      ],
    },
    {
      id: "copywriting",
      name: "Copywriting",
      icon: FileText,
      services: [
        {
          id: "landing-copy",
          title: "Copy para Landing Page",
          description:
            "Textos persuasivos que convierten visitantes en clientes con estructura optimizada",
          deliveryTime: "48h",
          icon: FileText,
          featured: true,
        },
        {
          id: "email-sequences",
          title: "Secuencia 5 Emails",
          description:
            "Secuencia completa con asuntos de alto impacto para maximizar apertura",
          deliveryTime: "48h",
          icon: Mail,
        },
        {
          id: "sales-page",
          title: "Página de Ventas",
          description:
            "Copy completo con estructura de alto impacto para maximizar conversiones",
          deliveryTime: "72h",
          icon: TrendingUp,
        },
      ],
    },
    {
      id: "contenido",
      name: "Contenido",
      icon: FileText,
      services: [
        {
          id: "blog-post",
          title: "Artículo Blog SEO",
          deliveryTime: "48h",
          icon: Search,
        },
        {
          id: "newsletter",
          title: "Newsletter semanal",
          deliveryTime: "48h",
          icon: Mail,
        },
        {
          id: "social-media-calendar",
          title: "Calendario Contenido (1 mes)",
          deliveryTime: "72h",
          icon: Columns,
        },
      ],
    },
    {
      id: "estrategia",
      name: "Estrategia",
      icon: Briefcase,
      services: [
        {
          id: "marketing-plan",
          title: "Plan Marketing Básico",
          deliveryTime: "72h",
          icon: BarChart2,
        },
        {
          id: "funnel-strategy",
          title: "Estrategia de Funnel",
          deliveryTime: "72h",
          icon: TrendingUp,
        },
        {
          id: "brand-messaging",
          title: "Guía Messaging de Marca",
          deliveryTime: "72h",
          icon: MessageSquare,
        },
      ],
    },
  ];

  // Todos los servicios para la tab "Todos"
  const allServices = serviceCategories.flatMap(
    (category) => category.services,
  );

  // Servicio para mostrar en base a la categoría seleccionada
  const displayServices =
    selectedTab === "todos"
      ? allServices
      : serviceCategories.find((cat) => cat.id === selectedTab)?.services || [];

  // Componente Tab para categoría
  const TabButton = ({
    id,
    label,
    icon: Icon,
  }: {
    id: string;
    label: string;
    icon?: LucideIcon;
  }) => {
    const isActive = selectedTab === id;

    return (
      <button
        className={`flex-1 py-3 px-4 text-center ${isActive ? "bg-gray-100 font-medium" : "bg-white hover:bg-gray-50"} rounded-md transition-colors`}
        onClick={() => setSelectedTab(id)}
      >
        <div className="flex items-center justify-center">
          {Icon && <Icon className="h-4 w-4 mr-2" />}
          <span>{label}</span>
        </div>
      </button>
    );
  };

  // Componente para tarjeta de servicio (exactamente como la imagen)
  const ServiceCard = ({ service }: { service: Service }) => {
    const {
      id,
      title,
      description,
      deliveryTime,
      icon: Icon,
      bgImage,
      popular,
      featured,
      stats,
    } = service;

    return (
      <div className="group bg-white rounded-md overflow-hidden shadow-sm border relative">
        {/* Área de imagen o fondo */}
        <div className="relative aspect-video overflow-hidden">
          {/* Usamos imágenes de fondo reales */}
          {/* Imágenes reales con URLs absolutas */}
          {id === "instagram-ads" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/35177/pexels-photo.jpg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Instagram Marketing"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "facebook-ads" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/4144923/pexels-photo-4144923.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Facebook Marketing"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "reels-tiktok" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/11710346/pexels-photo-11710346.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="TikTok Marketing"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "landing-copy" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Landing Page Design"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "email-sequences" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/193003/pexels-photo-193003.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Email Marketing"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "sales-page" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/6963944/pexels-photo-6963944.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Sales Page"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "blog-post" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/1591056/pexels-photo-1591056.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Blog Post"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "newsletter" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/5076531/pexels-photo-5076531.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Newsletter"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "social-media-calendar" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/6224/hands-people-woman-working.jpg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Social Media Calendar"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "marketing-plan" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/7947961/pexels-photo-7947961.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Marketing Plan"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "funnel-strategy" && (
            <div className="w-full h-full bg-gradient-to-br from-blue-600 via-indigo-500 to-purple-400 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2940&auto=format&fit=crop"
                alt="Funnel Strategy"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {id === "brand-messaging" && (
            <div className="w-full h-full overflow-hidden">
              <img
                src="https://images.pexels.com/photos/6476589/pexels-photo-6476589.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
                alt="Brand Messaging"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {![
            "instagram-ads",
            "facebook-ads",
            "reels-tiktok",
            "landing-copy",
            "email-sequences",
            "sales-page",
            "blog-post",
            "newsletter",
            "social-media-calendar",
            "marketing-plan",
            "funnel-strategy",
            "brand-messaging",
          ].includes(id) && (
            <div className="w-full h-full bg-gradient-to-br from-gray-600 via-slate-500 to-gray-400 flex items-center justify-center">
              <Icon className="text-white h-14 w-14" />
            </div>
          )}

          {/* Badges */}
          {popular && (
            <div className="absolute top-3 left-3">
              <Badge className="bg-yellow-500 text-white border-none">
                <Star className="mr-1 h-3 w-3" />
                Popular
              </Badge>
            </div>
          )}

          {featured && (
            <div className="absolute top-3 left-3">
              <Badge className="bg-purple-600 text-white border-none">
                <Sparkles className="mr-1 h-3 w-3" />
                Destacado
              </Badge>
            </div>
          )}

          {/* Overlay de botones que aparece al pasar el mouse */}
          <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <button className="bg-white text-black p-2 rounded-full mx-1 shadow-lg">
              <Heart className="h-4 w-4" />
            </button>
            <button className="bg-white text-black p-2 rounded-full mx-1 shadow-lg">
              <Share2 className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Contenido de la tarjeta */}
        <div className="p-4">
          <div className="mb-3">
            <div className="bg-gray-100 p-2 inline-block rounded-md">
              <Icon className="h-5 w-5 text-gray-700" />
            </div>
          </div>

          <h3 className="font-medium text-lg mb-2">{title}</h3>

          {description && (
            <p className="text-gray-600 text-sm mb-3">{description}</p>
          )}

          <div className="flex items-center text-sm text-gray-500 mb-3">
            <Clock className="h-4 w-4 mr-1.5" />
            <span>Entrega en {deliveryTime}</span>
          </div>

          {stats && (
            <div className="flex justify-between items-center mb-3 text-sm text-gray-500">
              {stats.views && (
                <div className="flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  <span>{stats.views}</span>
                </div>
              )}
              {stats.likes && (
                <div className="flex items-center">
                  <Heart className="h-4 w-4 mr-1" />
                  <span>{stats.likes}</span>
                </div>
              )}
            </div>
          )}

          {/* Banner "Por profesionales" */}
          <div className="bg-blue-50 rounded-lg p-2 mb-3 flex items-center">
            <Users className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" />
            <span className="text-xs text-blue-700 font-medium">
              Servicio realizado por profesionales
            </span>
          </div>

          <Button
            className="w-full mt-2 bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white"
            onClick={() => handleServiceSelect(title)}
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            Contratar servicio
          </Button>
        </div>
      </div>
    );
  };

  return (
    <DashboardLayout pageTitle="Emma AI Team - Servicios de Marketing">
      <div className="min-h-screen bg-[#f0f0f0] py-8 px-4 relative">
        {/* Background particles y grid pattern */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(15)].map((_, i) => (
            <div
              key={i}
              className="absolute bg-black/5 rounded-full"
              style={{
                width: `${Math.random() * 16 + 4}px`,
                height: `${Math.random() * 16 + 4}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
            />
          ))}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage:
                "linear-gradient(#00000008 1px, transparent 1px), linear-gradient(90deg, #00000008 1px, transparent 1px)",
              backgroundSize: "16px 16px",
            }}
          />
        </div>

        {/* Banner informativo y botón de regreso */}
        <div className="container mx-auto max-w-6xl mb-8 flex flex-col md:flex-row items-start justify-between relative z-10">
          <div className="bg-white border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] rounded-lg p-4 flex-grow mb-4 md:mb-0 md:mr-4">
            <div className="flex items-center">
              <div className="bg-blue-100 rounded-full p-2 mr-3 border-2 border-black">
                <AlertCircle className="h-5 w-5 text-blue-600" />
              </div>
              <p className="text-gray-800 font-bold">
                ¿No encuentras lo que buscas? Pide lo que necesites. Nuestro
                equipo puede personalizar cualquier servicio de marketing para
                ti.
              </p>
            </div>
          </div>

          <a
            href="/dashboard"
            className="bg-white border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-1 px-4 py-2 rounded-lg flex items-center transition-all duration-200"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            <span className="font-bold">Regresar al dashboard</span>
          </a>
        </div>

        {/* Encabezado estilo marketplace con explicación clara del propósito */}
        <div className="rounded-2xl bg-white border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] mb-12 overflow-hidden relative z-10">
          <div className="container mx-auto max-w-6xl py-8 px-6 relative">
            {/* Elementos decorativos de fondo */}
            <div className="absolute -right-5 -top-5 h-32 w-32 bg-blue-500 opacity-10 rounded-full blur-xl"></div>
            <div className="absolute right-1/4 bottom-0 h-24 w-24 bg-indigo-600 opacity-10 rounded-full blur-xl"></div>
            <div className="absolute left-1/3 top-1/4 h-16 w-16 bg-purple-500 opacity-10 rounded-full blur-lg"></div>

            <div className="relative">
              {/* Badge distintivo */}
              <div className="inline-block bg-gradient-to-r from-blue-600 to-indigo-600 px-4 py-1.5 rounded-full text-white text-xs font-medium mb-3 shadow-sm">
                Servicios Profesionales
              </div>

              <h1 className="text-3xl font-bold tracking-tight mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-500">
                Emma AI Team: Expertos a tu servicio
              </h1>

              <div className="flex items-center space-x-4 mb-4">
                <div className="h-1 w-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
                <span className="text-sm text-indigo-600 uppercase tracking-wider font-medium">
                  En menos de 48 horas
                </span>
              </div>

              {/* Banner explicativo con estilo de marketplace */}
              <div className="bg-white/80 backdrop-blur-md p-6 rounded-xl border border-blue-100 shadow-sm mb-6">
                <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                  <div className="bg-blue-100 p-3 rounded-full">
                    <Users className="h-7 w-7 text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                      Marketplace de servicios profesionales
                    </h2>
                    <p className="text-gray-700">
                      Contrata a nuestro equipo de expertos para las tareas que
                      prefieras no hacer tú mismo o donde la IA no es
                      suficiente. Selecciona un servicio, describe lo que
                      necesitas, y recibirás un trabajo profesional en menos de
                      48 horas.
                    </p>
                  </div>
                </div>
              </div>

              {/* Ventajas distintivas - estilo Fiverr */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-2">
                <div className="bg-white/70 backdrop-blur-sm p-4 rounded-lg border border-blue-50 flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-blue-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-700">
                    Entrega rápida en 24-48h
                  </span>
                </div>
                <div className="bg-white/70 backdrop-blur-sm p-4 rounded-lg border border-blue-50 flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-blue-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-700">
                    Calidad profesional garantizada
                  </span>
                </div>
                <div className="bg-white/70 backdrop-blur-sm p-4 rounded-lg border border-blue-50 flex items-center space-x-3">
                  <RefreshCw className="h-5 w-5 text-blue-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-700">
                    Incluye revisiones
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Pestañas de categorías - Estilo landing page */}
        <div className="container mx-auto max-w-6xl mb-8 relative z-10">
          <div className="flex flex-wrap justify-center gap-3">
            <button
              className={`py-2 px-5 rounded-full font-bold text-sm transition-all duration-200
                ${
                  selectedTab === "todos"
                    ? "bg-black text-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
                    : "bg-white border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-1"
                }`}
              onClick={() => setSelectedTab("todos")}
            >
              Todos los servicios
            </button>

            {serviceCategories.map((category) => (
              <button
                key={category.id}
                className={`py-2 px-5 rounded-full font-bold text-sm transition-all duration-200 flex items-center
                  ${
                    selectedTab === category.id
                      ? "bg-black text-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
                      : "bg-white border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)] hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-1"
                  }`}
                onClick={() => setSelectedTab(category.id)}
              >
                {React.createElement(category.icon, {
                  className: "h-4 w-4 mr-2",
                })}
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Grid de servicios - estilo landing page */}
        <div className="container mx-auto max-w-6xl mb-16 relative z-10">
          <h2 className="inline-block text-2xl font-black mb-6 bg-white px-4 py-2 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]">
            Servicios disponibles
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {displayServices.map((service, index) => (
              <div
                key={service.id}
                className="transform transition-all duration-300 hover:-translate-y-2"
                style={{ transitionDelay: `${index * 50}ms` }}
              >
                <ServiceCard key={service.id} service={service} />
              </div>
            ))}
          </div>
        </div>

        {/* Sección de Preguntas Frecuentes - Estilo landing page */}
        <div className="container mx-auto max-w-6xl mb-20 relative z-10">
          <h2 className="inline-block text-2xl font-black mb-8 bg-white px-4 py-2 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]">
            Preguntas frecuentes
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* FAQ 1 */}
            <div className="bg-white p-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] transform transition-all duration-300 hover:-translate-y-1 hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]">
              <h3 className="font-black text-lg mb-3">
                ¿Cómo funciona el servicio?
              </h3>
              <p className="text-gray-700">
                Selecciona el servicio que necesitas, completa un breve
                formulario con tus requisitos y nuestro equipo de profesionales
                comenzará a trabajar en tu proyecto. Recibirás actualizaciones
                sobre el progreso y el entregable final en el plazo indicado.
              </p>
            </div>

            {/* FAQ 2 */}
            <div className="bg-white p-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] transform transition-all duration-300 hover:-translate-y-1 hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]">
              <h3 className="font-black text-lg mb-3">
                ¿Qué pasa después de solicitar?
              </h3>
              <p className="text-gray-700">
                Tras enviar tu solicitud, recibirás una confirmación inmediata.
                Nuestro equipo revisará los detalles y podría contactarte si
                necesita información adicional. Trabajaremos en tu entregable y
                te notificaremos cuando esté listo para revisar.
              </p>
            </div>

            {/* FAQ 3 */}
            <div className="bg-white p-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] transform transition-all duration-300 hover:-translate-y-1 hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]">
              <h3 className="font-black text-lg mb-3">
                ¿Puedo solicitar revisiones?
              </h3>
              <p className="text-gray-700">
                Sí, cada servicio incluye hasta 2 rondas de revisiones sin costo
                adicional. Valoramos tu satisfacción y trabajaremos contigo
                hasta que estés completamente satisfecho con el resultado final.
              </p>
            </div>

            {/* FAQ 4 */}
            <div className="bg-white p-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] transform transition-all duration-300 hover:-translate-y-1 hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]">
              <h3 className="font-black text-lg mb-3">
                ¿Qué información debo proporcionar?
              </h3>
              <p className="text-gray-700">
                Para obtener los mejores resultados, te recomendamos
                proporcionar información sobre tu marca, público objetivo,
                objetivos específicos del proyecto y cualquier referencia o
                ejemplo que consideres relevante. Cuanta más información nos
                proporciones, mejor podremos satisfacer tus necesidades.
              </p>
            </div>
          </div>
        </div>

        {/* Comparación con agencias tradicionales */}
        <div className="container mx-auto max-w-6xl mb-16 relative z-10">
          <h2 className="inline-block text-2xl font-black mb-8 bg-white px-4 py-2 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]">
            ¿Por qué somos mejores que las agencias tradicionales?
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-10">
            {/* Agencias tradicionales */}
            <div className="bg-white p-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]">
              <div className="bg-red-100 p-3 inline-flex rounded-full mb-4">
                <Minus className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-xl font-black mb-4">
                Agencias tradicionales
              </h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Minus className="h-4 w-4 text-red-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Contratos largos (6-12 meses) sin flexibilidad
                  </span>
                </li>
                <li className="flex items-start">
                  <Minus className="h-4 w-4 text-red-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Precios inflados y tarifas mensuales caras
                  </span>
                </li>
                <li className="flex items-start">
                  <Minus className="h-4 w-4 text-red-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Tiempos de entrega lentos (semanas o meses)
                  </span>
                </li>
                <li className="flex items-start">
                  <Minus className="h-4 w-4 text-red-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Sin garantías de resultados concretos
                  </span>
                </li>
                <li className="flex items-start">
                  <Minus className="h-4 w-4 text-red-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Comunicación lenta y burocrática
                  </span>
                </li>
              </ul>
            </div>

            {/* Emma AI Team */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]">
              <div className="bg-blue-100 p-3 inline-flex rounded-full mb-4">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-black mb-4">Emma AI Team</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Servicios específicos cuando los necesites
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Precios transparentes y asequibles por servicio
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Entregas rápidas (24-48h) garantizadas
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Expertos en marketing y creatividad
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Integración perfecta con nuestra plataforma AI
                  </span>
                </li>
              </ul>
            </div>

            {/* Marwick */}
            <div className="bg-gradient-to-br from-gray-50 to-slate-100 p-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]">
              <div className="bg-gray-200 p-3 inline-flex rounded-full mb-4">
                <FileText className="h-6 w-6 text-gray-700" />
              </div>
              <h3 className="text-xl font-black mb-4">Marwick Tech</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Servicios técnicos y desarrollos personalizados
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Implementación efectiva de soluciones técnicas
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Desarrollo de agentes de IA especializados
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Soporte técnico y mantenimiento continuo
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">
                    Sinergias con el equipo de marketing de Emma
                  </span>
                </li>
              </ul>
            </div>
          </div>

          <div className="bg-yellow-50 p-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] mb-8">
            <div className="flex items-center mb-3">
              <AlertCircle className="h-6 w-6 text-amber-600 mr-2" />
              <h3 className="text-lg font-black">
                ¡Deja de desperdiciar tu dinero en agencias tradicionales!
              </h3>
            </div>
            <p className="text-gray-700 mb-4">
              Con Emma AI Team obtienes resultados más rápidos, mejores y a una
              fracción del costo de las agencias de marketing tradicionales. Sin
              contratos largos, sin compromisos mensuales forzosos, solo
              servicios profesionales cuando los necesites.
            </p>
          </div>
        </div>

        {/* Opción de marketing completo - Destacado */}
        <div className="container mx-auto max-w-6xl mb-10 relative z-10">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-8 rounded-xl border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)]">
            <div className="flex flex-col md:flex-row items-center">
              <div className="mb-6 md:mb-0 md:mr-6 md:w-2/3">
                <div className="inline-flex items-center bg-blue-100 px-3 py-1 rounded-full border-2 border-black mb-3">
                  <Star className="h-4 w-4 mr-1 text-blue-600 fill-blue-600" />
                  <span className="text-sm font-black">Servicio destacado</span>
                </div>
                <h2 className="text-2xl font-black mb-3">
                  Deja que Emma AI se encargue de TODO tu marketing
                </h2>
                <p className="text-gray-700 mb-4">
                  Nuestro equipo de expertos puede gestionar toda la estrategia
                  y ejecución del marketing de tu empresa. Desde el contenido y
                  las redes sociales hasta el diseño y las campañas
                  publicitarias. Tú te concentras en tu negocio, nosotros en
                  hacerlo crecer.
                </p>
                <div className="flex flex-wrap gap-3 mb-4">
                  <div className="flex items-center">
                    <CheckCircle
                      size={16}
                      className="text-green-600 mr-1 flex-shrink-0"
                    />
                    <span className="text-sm font-bold">
                      Estrategia completa
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle
                      size={16}
                      className="text-green-600 mr-1 flex-shrink-0"
                    />
                    <span className="text-sm font-bold">
                      Creación de contenido
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle
                      size={16}
                      className="text-green-600 mr-1 flex-shrink-0"
                    />
                    <span className="text-sm font-bold">Gestión de redes</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle
                      size={16}
                      className="text-green-600 mr-1 flex-shrink-0"
                    />
                    <span className="text-sm font-bold">
                      Campañas publicitarias
                    </span>
                  </div>
                </div>
              </div>
              <div className="md:w-1/3 flex justify-center">
                <Button
                  className="py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-black rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] transform transition-all duration-300 hover:-translate-y-1 hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]"
                  onClick={() => handleServiceSelect("Marketing completo")}
                >
                  <ShoppingCart className="mr-2 h-5 w-5" />
                  Solicitar plan personalizado
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* CTA final - Estilo de landing page */}
        <div className="container mx-auto max-w-6xl mb-8 text-center relative z-10">
          <div className="bg-white p-8 rounded-xl border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)]">
            <h2 className="text-2xl font-black mb-4">
              ¿Necesitas ayuda con un proyecto específico?
            </h2>
            <p className="text-lg max-w-2xl mx-auto mb-6">
              Nuestro equipo de profesionales está listo para ayudarte con
              cualquier proyecto de marketing, diseño o contenido que necesites.
            </p>
            <Button
              className="py-3 px-8 bg-black hover:bg-gray-800 text-white font-black rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] transform transition-all duration-300 hover:-translate-y-1 hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]"
              onClick={() => handleServiceSelect("Servicio personalizado")}
            >
              <ShoppingCart className="mr-2 h-5 w-5" />
              Contrata a nuestro equipo ahora
            </Button>
          </div>
        </div>

        {/* Botón para regresar a servicios profesionales */}
        <div className="container mx-auto max-w-6xl mb-16 flex justify-center relative z-10">
          <a
            href="/dashboard/servicios"
            className="flex items-center bg-white py-3 px-6 rounded-full border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] transform transition-all duration-300 hover:-translate-y-1 hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            <span className="font-bold">
              Regresar a Servicios Profesionales
            </span>
          </a>
        </div>
      </div>
    </DashboardLayout>
  );
}
