import React from "react";
import { motion } from "framer-motion";
import { CheckCircle } from "lucide-react";
import { agentEmmaDefinitionCards, digitalProfessionalFeatures } from "./data";

const WhatIsSection: React.FC = () => {
  return (
    <section id="que-es" className="py-24 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl sm:text-5xl font-black mb-6">
                ¿Qué es un <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"><PERSON><PERSON> Emma</span>?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Un Agente Emma es un profesional digital entrenado para ejecutar tareas clave de marketing con precisión quirúrgica.
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {agentEmmaDefinitionCards.map((card, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-8 text-center"
                whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
              >
                <div className="flex justify-center mb-6">
                  <div className={`${card.bgColor} p-4 rounded-full border-3 border-black`}>
                    {card.icon}
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-4">{card.title}</h3>
                <p className="text-gray-600">
                  {card.description}
                </p>
              </motion.div>
            ))}
          </div>

          <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-8 text-white">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-3xl font-black mb-6">🔍 ¿Qué hace?</h3>
                <ul className="space-y-4">
                  {digitalProfessionalFeatures.map((feature, index) => (
                    <motion.li 
                      key={index}
                      className="flex items-start gap-3"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <div className="bg-white p-1 rounded-full mt-1">
                        <CheckCircle className="text-purple-600" size={20} />
                      </div>
                      <div>
                        <h4 className="font-bold text-xl">{feature.title}</h4>
                        <p>{feature.description}</p>
                      </div>
                    </motion.li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-3xl font-black mb-6">🧠 ¿Cómo funciona?</h3>
                <ul className="space-y-4">
                  <motion.li 
                    className="flex items-start gap-3"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="bg-white p-1 rounded-full mt-1">
                      <CheckCircle className="text-purple-600" size={20} />
                    </div>
                    <p>Piensa en él como el nuevo integrante de tu equipo.</p>
                  </motion.li>
                  <motion.li 
                    className="flex items-start gap-3"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <div className="bg-white p-1 rounded-full mt-1">
                      <CheckCircle className="text-purple-600" size={20} />
                    </div>
                    <p>Lo ves, lo activas y empieza a trabajar contigo como si lo hubieras contratado desde LinkedIn.</p>
                  </motion.li>
                  <motion.li 
                    className="flex items-start gap-3"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <div className="bg-white p-1 rounded-full mt-1">
                      <CheckCircle className="text-purple-600" size={20} />
                    </div>
                    <p>Solo que este… no necesita onboarding. Ya viene listo.</p>
                  </motion.li>
                  <motion.li 
                    className="flex items-start gap-3"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <div className="bg-white p-1 rounded-full mt-1">
                      <CheckCircle className="text-purple-600" size={20} />
                    </div>
                    <p>Disponible 24/7, con calidad consistente y velocidad inhumana.</p>
                  </motion.li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhatIsSection;
