import React from "react";
import { motion } from "framer-motion";
import { benefits } from "./data";

const Benefits: React.FC = () => {
  return (
    <section id="beneficios" className="py-24 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl sm:text-5xl font-black mb-6">
            Beneficios Revolucionarios
          </h2>
          <p className="text-xl text-gray-600">
            Los Agentes Emma transforman tu negocio con capacidades que superan a cualquier herramienta o servicio tradicional.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {benefits.map((benefit, index) => (
            <motion.div 
              key={index}
              className="bg-white rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6"
              whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className={`${benefit.color} w-16 h-16 rounded-full flex items-center justify-center text-white border-4 border-black mb-4`}>
                {benefit.icon}
              </div>
              <h3 className="text-2xl font-bold mb-3">{benefit.title}</h3>
              <p className="text-gray-600">{benefit.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Benefits;
