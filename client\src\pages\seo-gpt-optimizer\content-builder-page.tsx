/**
 * SEO & GPT Optimizer™ - Content Builder Page
 * Main page for the content builder with real-time analysis
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Save } from 'lucide-react';
import { useLocation, useRoute } from 'wouter';

import { useProjects } from '../../hooks/seo-gpt-optimizer/useProjects';

import ErrorBoundary from '../../components/seo-gpt-optimizer/shared/ErrorBoundary';
import { ContentBuilder } from '../../components/seo-gpt-optimizer/content-builder';

const ContentBuilderPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const [match, params] = useRoute('/dashboard/herramientas/seo-gpt-optimizer/content-builder/:projectId');
  const projectId = params?.projectId;
  
  const [content, setContent] = useState('');
  const [topic, setTopic] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const {
    currentProject,
    loadProject,
    updateProject,
    loading: projectLoading
  } = useProjects();

  // Load project if projectId is provided
  useEffect(() => {
    if (projectId) {
      loadProject(projectId);
    }
  }, [projectId, loadProject]);

  // Set initial content from project
  useEffect(() => {
    if (currentProject) {
      setContent(currentProject.content_text || '');
      setTopic(currentProject.topic || '');
    }
  }, [currentProject]);

  const handleBack = () => {
    if (hasUnsavedChanges) {
      const confirmLeave = window.confirm(
        'Tienes cambios sin guardar. ¿Estás seguro de que quieres salir?'
      );
      if (!confirmLeave) return;
    }
    setLocation('/dashboard/herramientas/seo-gpt-optimizer');
  };

  const handleSave = async (contentToSave: string) => {
    if (projectId && currentProject) {
      try {
        await updateProject(projectId, {
          content_text: contentToSave,
          content_length: contentToSave.length,
          word_count: contentToSave.trim().split(/\s+/).filter(w => w.length > 0).length,
          updated_at: new Date().toISOString()
        });
        setHasUnsavedChanges(false);
      } catch (error) {
        console.error('Error saving project:', error);
      }
    }
  };

  const handleExport = () => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${topic || 'content'}-${Date.now()}.txt`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleToggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
  };

  // Sin keyboard shortcuts molestos

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-full px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    {currentProject ? currentProject.title : 'Content Builder'}
                  </h1>
                  <p className="text-gray-600 text-sm">
                    {currentProject ? currentProject.topic : 'Crea contenido optimizado con análisis en tiempo real'}
                  </p>
                </div>
                {hasUnsavedChanges && (
                  <div className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-lg text-xs font-medium">
                    Cambios sin guardar
                  </div>
                )}
              </div>

              <div className="flex items-center gap-3">
                {/* Save Button */}
                <button
                  onClick={() => handleSave(content)}
                  disabled={!hasUnsavedChanges}
                  className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white rounded-lg hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  <Save className="w-4 h-4" />
                  <span className="text-sm">Guardar</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Editor Simple */}
        <div className="h-[calc(100vh-80px)]">
          <ContentBuilder
            projectId={projectId || 'demo'}
            initialContent={content}
            initialTopic={topic}
            onContentChange={(newContent) => {
              setContent(newContent);
              setHasUnsavedChanges(true);
            }}
            className="h-full"
          />
        </div>


      </div>
    </ErrorBoundary>
  );
};

export default ContentBuilderPage;
